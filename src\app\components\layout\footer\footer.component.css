/* Enhanced <PERSON><PERSON> Styles with <PERSON><PERSON><PERSON> Art Inspiration */

/* Gradient animations */
@keyframes gradientShift {
  0%, 100% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
}

.gradient-animated {
  background-size: 200% 200%;
  animation: gradientShift 6s ease infinite;
}

/* Social media hover effects */
.social-link {
  position: relative;
  overflow: hidden;
}

.social-link::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.social-link:hover::before {
  left: 100%;
}

/* Newsletter input focus effects */
.newsletter-input:focus {
  box-shadow: 0 0 0 3px rgba(193, 68, 14, 0.1);
  transform: translateY(-1px);
}

/* Link hover animations */
.footer-link {
  position: relative;
  transition: all 0.3s ease;
}

.footer-link::after {
  content: '';
  position: absolute;
  bottom: -2px;
  left: 0;
  width: 0;
  height: 2px;
  background: linear-gradient(90deg, #C1440E, #F4B400);
  transition: width 0.3s ease;
}

.footer-link:hover::after {
  width: 100%;
}

/* Contact card hover effects */
.contact-card {
  transition: all 0.3s ease;
  border-left: 3px solid transparent;
}

.contact-card:hover {
  border-left-color: #C1440E;
  transform: translateX(4px);
}

/* Decorative elements */
.decorative-border {
  background: linear-gradient(90deg, transparent, #C1440E, #F4B400, #C1440E, transparent);
  height: 1px;
}

/* Newsletter benefits animation */
.benefit-item {
  opacity: 0;
  transform: translateX(-10px);
  animation: slideInLeft 0.5s ease forwards;
}

.benefit-item:nth-child(1) { animation-delay: 0.1s; }
.benefit-item:nth-child(2) { animation-delay: 0.2s; }
.benefit-item:nth-child(3) { animation-delay: 0.3s; }

@keyframes slideInLeft {
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* Heart beat animation */
@keyframes heartbeat {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.1);
  }
}

.heart-beat {
  animation: heartbeat 1.5s ease-in-out infinite;
}

/* Floating animation for decorative elements */
@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

.float-animation {
  animation: float 6s ease-in-out infinite;
}

/* Glow effect for important elements */
.glow-effect {
  box-shadow: 0 0 20px rgba(193, 68, 14, 0.3);
  transition: box-shadow 0.3s ease;
}

.glow-effect:hover {
  box-shadow: 0 0 30px rgba(193, 68, 14, 0.5);
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .footer-grid {
    grid-template-columns: 1fr;
    gap: 2rem;
  }

  .social-links {
    justify-content: center;
  }

  .footer-bottom {
    text-align: center;
  }

  .footer-bottom .flex {
    flex-direction: column;
    gap: 1rem;
  }
}

/* Custom scrollbar for newsletter section */
.newsletter-section::-webkit-scrollbar {
  width: 4px;
}

.newsletter-section::-webkit-scrollbar-track {
  background: rgba(193, 68, 14, 0.1);
}

.newsletter-section::-webkit-scrollbar-thumb {
  background: rgba(193, 68, 14, 0.3);
  border-radius: 2px;
}

.newsletter-section::-webkit-scrollbar-thumb:hover {
  background: rgba(193, 68, 14, 0.5);
}

/* Accessibility improvements */
.footer-link:focus,
.social-link:focus {
  outline: 2px solid #C1440E;
  outline-offset: 2px;
}

/* Print styles */
@media print {
  footer {
    background: white !important;
    color: black !important;
  }

  .decorative-elements {
    display: none;
  }
}
