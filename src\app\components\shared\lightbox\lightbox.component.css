/* Lightbox specific styles */
.lightbox-overlay {
  backdrop-filter: blur(4px);
}

/* Smooth transitions for navigation buttons */
button {
  transition: all 0.2s ease-in-out;
}

button:hover {
  transform: scale(1.1);
}

/* Custom scrollbar for thumbnail navigation */
.overflow-x-auto::-webkit-scrollbar {
  height: 4px;
}

.overflow-x-auto::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 2px;
}

.overflow-x-auto::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.3);
  border-radius: 2px;
}

.overflow-x-auto::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.5);
}

/* Image loading animation */
@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

.animate-pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

/* Ensure proper z-index stacking */
.z-60 {
  z-index: 60;
}

/* Mobile responsive adjustments */
@media (max-width: 768px) {
  .max-w-7xl {
    max-width: 95vw;
  }
  
  .max-h-[80vh] {
    max-height: 70vh;
  }
  
  .w-16.h-16 {
    width: 3rem;
    height: 3rem;
  }
}
