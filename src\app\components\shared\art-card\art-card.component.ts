import { Component, Input, Output, EventEmitter } from '@angular/core';
import { RouterLink } from '@angular/router';
import { CommonModule } from '@angular/common';
import { MithilaDecorativeElementComponent } from '../mithila-decorative-element/mithila-decorative-element.component';

@Component({
  selector: 'app-art-card',
  standalone: true,
  imports: [RouterLink, CommonModule, MithilaDecorativeElementComponent],
  templateUrl: './art-card.component.html',
  styleUrl: './art-card.component.css'
})
export class ArtCardComponent {
  @Input() id: string = '';
  @Input() title: string = '';
  @Input() artist: string = '';
  @Input() artistId: string = '';
  @Input() imageUrl: string = '';
  @Input() price: number = 0;
  @Input() medium: string = '';
  @Input() size: string = '';
  @Input() inStock: boolean = true;
  @Input() featured: boolean = false;
  @Input() category: string = '';
  @Input() isInCart: boolean = false;

  @Output() addToCart = new EventEmitter<string>();
  @Output() toggleWishlist = new EventEmitter<string>();

  isWishlisted: boolean = false;

  onAddToCart(event: Event) {
    event.preventDefault();
    event.stopPropagation();
    this.addToCart.emit(this.id);
  }

  onToggleWishlist(event: Event) {
    event.preventDefault();
    event.stopPropagation();
    this.isWishlisted = !this.isWishlisted;
    this.toggleWishlist.emit(this.id);
  }

  formatPrice(price: number): string {
    return `NPR ${price.toLocaleString()}`;
  }
}
