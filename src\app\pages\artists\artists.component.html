<!-- Enhanced <PERSON> Banner with <PERSON><PERSON>la Art Elements -->
<div class="relative h-[60vh] md:h-[70vh] overflow-hidden">
  <!-- Background Image with Parallax Effect -->
  <div class="absolute inset-0 bg-cover bg-center bg-no-repeat transform transition-transform duration-10000 hover:scale-105"
       style="background-image: url('https://i.etsystatic.com/43638819/r/il/bba73f/5930357734/il_794xN.5930357734_1ql7.jpg')">
  </div>

  <!-- Dark Overlay -->
  <div class="absolute inset-0 bg-black bg-opacity-50"></div>

  <!-- Decorative Border -->
  <app-mithila-border
    [primaryColor]="'#C1440E'"
    [secondaryColor]="'#F4B400'"
    [type]="'full'"
    position="top-8 left-8 right-8 bottom-8">
  </app-mithila-border>

  <!-- Content -->
  <div class="container mx-auto px-4 h-full flex flex-col justify-center items-center text-center relative z-10">
    <!-- Decorative Element -->
    <app-mithila-decorative-element
      [primaryColor]="'#C1440E'"
      [secondaryColor]="'#F4B400'"
      [type]="'lotus'"
      position="relative mb-6"
      classes="opacity-90 animate-float-slow"
      size="80px">
    </app-mithila-decorative-element>

    <!-- Title with Animation -->
    <h1 class="text-3xl sm:text-4xl md:text-5xl lg:text-6xl font-bold text-white mb-4 font-heading animate-fade-in">
      Our Artists
    </h1>

    <p class="text-xl text-white/90 mb-8 max-w-3xl mx-auto">
      Meet the talented artists who preserve and innovate the rich tradition of Mithila art
    </p>
  </div>
</div>


<!-- Featured Artists Section -->
<app-mithila-section
  primaryColor="#F4B400"
  secondaryColor="#264653"
  backgroundGradient="from-secondary-50 via-background-light to-accent-50"
  backgroundOpacity="15"
  padding="py-16 sm:py-20 md:py-24"
  [showDecorativeElements]="true">

  <app-section-title
    title="Our Artists"
    subtitle="The talented individuals behind our Mithila artwork"
  ></app-section-title>

  <!-- Filter Controls -->
  <div class="flex flex-wrap justify-center gap-2 mb-12">
    <button
      (click)="setActiveCategory('all')"
      class="px-4 py-2 rounded-full text-sm font-medium transition-colors duration-300"
      [ngClass]="activeCategory === 'all' ? 'bg-primary-600 text-white' : 'bg-gray-100 text-gray-700 hover:bg-gray-200'">
      All Artists
    </button>
    <button
      *ngFor="let category of artistCategories"
      (click)="setActiveCategory(category.id)"
      class="px-4 py-2 rounded-full text-sm font-medium transition-colors duration-300"
      [ngClass]="activeCategory === category.id ? 'bg-primary-600 text-white' : 'bg-gray-100 text-gray-700 hover:bg-gray-200'">
      {{category.name}}
    </button>
  </div>

  <!-- Artists Grid -->
  <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
    <div *ngFor="let artist of filteredArtists" class="bg-white rounded-lg overflow-hidden shadow-lg artist-card group">
      <div class="relative overflow-hidden h-64">
        <!-- Artist Image -->
        <img [src]="artist.image" [alt]="artist.name" class="w-full h-full object-cover object-center transition-transform duration-700 group-hover:scale-110">

        <!-- Featured Badge -->
        <div *ngIf="artist.featured" class="absolute top-4 left-4 bg-primary-600 text-white px-3 py-1 rounded-full text-sm font-bold">
          Featured
        </div>

        <!-- Workshop Badge -->
        <div *ngIf="artist.workshops" class="absolute top-4 right-4 bg-secondary-600 text-white px-3 py-1 rounded-full text-sm font-bold">
          Workshops
        </div>

        <!-- Overlay Gradient -->
        <div class="absolute inset-0 bg-gradient-to-t from-gray-900/70 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
      </div>

      <div class="p-6">
        <h3 class="text-xl font-bold text-gray-900 group-hover:text-primary-600 transition-colors duration-300">{{artist.name}}</h3>
        <p class="text-primary-600 mb-3 font-medium">{{artist.role}}</p>
        <p class="text-gray-700 mb-4 text-sm line-clamp-3">{{artist.bio}}</p>
        <p class="text-gray-600 text-sm mb-4"><strong>Specialization:</strong> {{artist.specialization}}</p>

        <div class="flex justify-between items-center">
          <a [routerLink]="['/artists', artist.id]" class="text-primary-600 hover:text-primary-700 font-medium">
            View Profile
          </a>

          <div class="flex space-x-2">
            <a href="#" class="text-gray-400 hover:text-primary-600 transition-colors duration-300">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="currentColor" viewBox="0 0 24 24">
                <path d="M24 4.557c-.883.392-1.832.656-2.828.775 1.017-.609 1.798-1.574 2.165-2.724-.951.564-2.005.974-3.127 1.195-.897-.957-2.178-1.555-3.594-1.555-3.179 0-5.515 2.966-4.797 6.045-4.091-.205-7.719-2.165-10.148-5.144-1.29 2.213-.669 5.108 1.523 6.574-.806-.026-1.566-.247-2.229-.616-.054 2.281 1.581 4.415 3.949 4.89-.693.188-1.452.232-2.224.084.626 1.956 2.444 3.379 4.6 3.419-2.07 1.623-4.678 2.348-7.29 2.04 2.179 1.397 4.768 2.212 7.548 2.212 9.142 0 14.307-7.721 13.995-14.646.962-.695 1.797-1.562 2.457-2.549z"/>
              </svg>
            </a>
            <a href="#" class="text-gray-400 hover:text-primary-600 transition-colors duration-300">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="currentColor" viewBox="0 0 24 24">
                <path d="M12 2.163c3.204 0 3.584.012 4.85.07 3.252.148 4.771 1.691 4.919 4.919.058 1.265.069 1.645.069 4.849 0 3.205-.012 3.584-.069 4.849-.149 3.225-1.664 4.771-4.919 4.919-1.266.058-1.644.07-4.85.07-3.204 0-3.584-.012-4.849-.07-3.26-.149-4.771-1.699-4.919-4.92-.058-1.265-.07-1.644-.07-4.849 0-3.204.013-3.583.07-4.849.149-3.227 1.664-4.771 4.919-4.919 1.266-.057 1.645-.069 4.849-.069zm0-2.163c-3.259 0-3.667.014-4.947.072-4.358.2-6.78 2.618-6.98 6.98-.059 1.281-.073 1.689-.073 4.948 0 3.259.014 3.668.072 4.948.2 4.358 2.618 6.78 6.98 6.98 1.281.058 1.689.072 4.948.072 3.259 0 3.668-.014 4.948-.072 4.354-.2 6.782-2.618 6.979-6.98.059-1.28.073-1.689.073-4.948 0-3.259-.014-3.667-.072-4.947-.196-4.354-2.617-6.78-6.979-6.98-1.281-.059-1.69-.073-4.949-.073zm0 5.838c-3.403 0-6.162 2.759-6.162 6.162s2.759 6.163 6.162 6.163 6.162-2.759 6.162-6.163c0-3.403-2.759-6.162-6.162-6.162zm0 10.162c-2.209 0-4-1.79-4-4 0-2.209 1.791-4 4-4s4 1.791 4 4c0 2.21-1.791 4-4 4zm6.406-11.845c-.796 0-1.441.645-1.441 1.44s.645 1.44 1.441 1.44c.795 0 1.439-.645 1.439-1.44s-.644-1.44-1.439-1.44z"/>
              </svg>
            </a>
          </div>
        </div>
      </div>
    </div>
  </div>
</app-mithila-section>

<!-- Artist Spotlight Section -->
<app-mithila-section
  primaryColor="#264653"
  secondaryColor="#3B945E"
  backgroundGradient="from-accent-50 via-background-light to-primary-50"
  backgroundOpacity="15"
  padding="py-16 sm:py-20 md:py-24"
  [showDecorativeElements]="true">

  <app-section-title
    title="Artist Spotlight"
    subtitle="Featuring Sarita Devi, Founder & Master Artist"
  ></app-section-title>

  <div class="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
    <div>
      <div class="relative rounded-lg overflow-hidden group">
        <!-- Decorative Border -->
        <div class="absolute -inset-1 bg-gradient-to-br from-primary-300 via-secondary-300 to-accent-300 rounded-lg blur-sm animate-border-gradient"></div>

        <div class="relative rounded-lg overflow-hidden">
          <img src="https://i.etsystatic.com/43638819/r/il/bba73f/5930357734/il_794xN.5930357734_1ql7.jpg" alt="Sarita Devi" class="w-full h-auto transition-transform duration-700 group-hover:scale-105">

          <!-- Overlay Gradient -->
          <div class="absolute inset-0 bg-gradient-to-t from-primary-900/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
        </div>
      </div>
    </div>

    <div>
      <h3 class="text-2xl font-bold text-gray-900 mb-4">Sarita Devi</h3>
      <p class="text-primary-600 mb-6 font-medium">Master Artist & Founder</p>

      <div class="space-y-4 text-gray-700">
        <p>Sarita Devi began her artistic journey at the age of 12, learning traditional Mithila painting techniques from her grandmother. Born in a small village near Janakpur, she grew up surrounded by the rich cultural traditions of the Mithila region.</p>
        <p>After completing her formal education, she dedicated herself to mastering and preserving this ancient art form. In 2015, she established Mithilani Ghar with the vision of creating a space where artists could work, teach, and showcase their creations.</p>
        <p>Her paintings are characterized by intricate details, vibrant colors, and powerful storytelling that often explores themes of rural life, mythology, and women's experiences.</p>
      </div>

      <div class="mt-8">
        <h4 class="text-lg font-semibold text-gray-900 mb-3">Notable Achievements</h4>
        <ul class="space-y-2 text-gray-700">
          <li class="flex items-start">
            <span class="text-primary-600 mr-2">•</span>
            <span>National Award for Excellence in Traditional Arts (2018)</span>
          </li>
          <li class="flex items-start">
            <span class="text-primary-600 mr-2">•</span>
            <span>Featured artist at the South Asian Art Festival, London (2019)</span>
          </li>
          <li class="flex items-start">
            <span class="text-primary-600 mr-2">•</span>
            <span>Published in "Contemporary Folk Artists of South Asia" (2020)</span>
          </li>
        </ul>
      </div>

      <div class="mt-8">
        <a [routerLink]="['/artists', '1']" class="inline-block px-6 py-3 bg-primary-600 text-white rounded-lg hover:bg-primary-700 transition-colors duration-300">
          View Full Profile
        </a>
      </div>
    </div>
  </div>
</app-mithila-section>

<!-- Join Our Community CTA -->
<app-mithila-section
  primaryColor="#3B945E"
  secondaryColor="#F4B400"
  backgroundGradient="from-primary-50 via-background-light to-secondary-50"
  backgroundOpacity="10"
  padding="py-16 sm:py-20 md:py-24"
  [showDecorativeElements]="true">

  <div class="max-w-4xl mx-auto">
    <div class="bg-gradient-to-r from-accent-700 to-primary-700 rounded-lg p-8 shadow-lg text-white relative overflow-hidden">
      <!-- Dark Overlay for Better Text Visibility -->
      <div class="absolute inset-0 bg-black/30"></div>

      <!-- Decorative Background Pattern -->
      <div class="absolute inset-0 opacity-5">
        <app-mithila-art-background
          [primaryColor]="'#FFFFFF'"
          [secondaryColor]="'#FFFFFF'"
          opacity="5">
        </app-mithila-art-background>
      </div>

      <div class="relative z-10 text-center">
        <app-mithila-decorative-element
          [primaryColor]="'#3B945E'"
          [secondaryColor]="'#F4B400'"
          [type]="'lotus'"
          position="relative mx-auto mb-6"
          classes="opacity-90"
          size="60px">
        </app-mithila-decorative-element>

        <h2 class="text-3xl font-bold mb-4 text-white drop-shadow-md">Become Part of Our Artist Community</h2>
        <p class="mb-8 text-white font-medium text-lg drop-shadow-md max-w-2xl mx-auto">
          Mithilani Ghar welcomes artists at all levels who are passionate about Mithila art. Join our workshops, participate in exhibitions, or apply to become a resident artist.
        </p>

        <div class="flex flex-col sm:flex-row justify-center gap-4">
          <a routerLink="/contact" class="btn bg-white text-primary-600 hover:bg-gray-100 hover:shadow-lg transition-all duration-300 transform hover:-translate-y-1">Contact Us</a>
          <a routerLink="/events" class="btn bg-primary-600 text-white border border-white hover:bg-primary-700 hover:shadow-lg transition-all duration-300 transform hover:-translate-y-1">View Events</a>
        </div>
      </div>
    </div>
  </div>
</app-mithila-section>
