<!-- <PERSON> Banner -->
<div class="relative h-[50vh] overflow-hidden">
  <!-- Background Image -->
  <div class="absolute inset-0 bg-cover bg-center bg-no-repeat"
       style="background-image: url('https://i.etsystatic.com/43638819/r/il/bba73f/5930357734/il_794xN.5930357734_1ql7.jpg')">
  </div>

  <!-- Gradient Overlay -->
  <div class="absolute inset-0 bg-gradient-to-br from-primary-800/90 via-primary-700/90 to-brick-700/90"></div>

  <!-- <PERSON><PERSON><PERSON> Background -->
  <div class="absolute inset-0 opacity-10">
    <app-mithila-art-background
      [primaryColor]="'#C1440E'"
      [secondaryColor]="'#F4B400'"
      opacity="15">
    </app-mithila-art-background>
  </div>

  <!-- Content -->
  <div class="container mx-auto px-4 h-full flex flex-col justify-center items-center text-center relative z-10">
    <!-- Decorative Element -->
    <app-mithila-decorative-element
      [primaryColor]="'#C1440E'"
      [secondaryColor]="'#F4B400'"
      [type]="'lotus'"
      position="relative mb-6"
      classes="opacity-90"
      size="80px">
    </app-mithila-decorative-element>

    <!-- Title -->
    <h1 class="text-3xl sm:text-4xl md:text-5xl lg:text-6xl font-bold text-white mb-4">
      Our Artists
    </h1>

    <!-- Subtitle -->
    <p class="text-xl text-white/90 mb-8 max-w-3xl mx-auto">
      Meet the talented artists and shop their beautiful creations
    </p>
  </div>
</div>

<!-- Artists Section -->
<app-mithila-section
  primaryColor="#F4B400"
  secondaryColor="#264653"
  backgroundGradient="from-secondary-50 via-background-light to-accent-50"
  backgroundOpacity="15"
  padding="py-16 sm:py-20 md:py-24"
  [showDecorativeElements]="true">

  <app-section-title
    title="Our Artists"
    subtitle="Meet our talented artists and shop their beautiful creations"
  ></app-section-title>

  <!-- Artists Grid -->
  <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-8">
    <div *ngFor="let artist of featuredArtists" class="bg-white rounded-lg overflow-hidden shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-2">
      <!-- Artist Image -->
      <div class="relative overflow-hidden h-64">
        <img [src]="artist.image" [alt]="artist.name" class="w-full h-full object-cover object-center transition-transform duration-700 hover:scale-110">

        <!-- Overlay -->
        <div class="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent opacity-0 hover:opacity-100 transition-opacity duration-300"></div>

        <!-- Artist Info Overlay -->
        <div class="absolute bottom-0 left-0 right-0 p-4 text-white transform translate-y-full hover:translate-y-0 transition-transform duration-300">
          <h3 class="font-bold text-lg mb-1">{{artist.name}}</h3>
          <p class="text-sm opacity-90">{{artist.role}}</p>
        </div>
      </div>

      <!-- Artist Details -->
      <div class="p-6">
        <h3 class="text-xl font-bold text-gray-900 mb-2">{{artist.name}}</h3>
        <p class="text-primary-600 mb-3 font-medium">{{artist.role}}</p>
        <p class="text-gray-700 mb-4 text-sm line-clamp-3">{{artist.bio}}</p>

        <!-- Artworks -->
        <div class="space-y-3">
          <h4 class="font-semibold text-gray-900 text-sm">Available Artworks:</h4>
          <div *ngFor="let artwork of artist.artworks" class="flex items-center justify-between p-3 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors duration-200">
            <div class="flex-1">
              <p class="font-medium text-gray-900 text-sm">{{artwork.name}}</p>
              <p class="text-primary-600 font-bold text-sm">{{formatPrice(artwork.price)}}</p>
            </div>
            <button
              (click)="addToCart(artwork, $event)"
              [disabled]="isInCart(artwork.id)"
              class="px-3 py-1 text-xs font-medium rounded-full transition-all duration-200"
              [ngClass]="isInCart(artwork.id) ?
                'bg-green-100 text-green-700 cursor-not-allowed' :
                'bg-primary-600 text-white hover:bg-primary-700 hover:shadow-md transform hover:scale-105'">
              {{isInCart(artwork.id) ? 'In Cart' : 'Add to Cart'}}
            </button>
          </div>
        </div>

        <!-- View Profile Link -->
        <div class="mt-4 pt-4 border-t border-gray-200">
          <a [routerLink]="['/artists', artist.id]" class="text-primary-600 hover:text-primary-700 font-medium text-sm flex items-center">
            View Full Profile
            <svg class="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
            </svg>
          </a>
        </div>
      </div>
    </div>
  </div>
</app-mithila-section>

<!-- Join as Artist Section -->
<app-mithila-section
  primaryColor="#C1440E"
  secondaryColor="#F4B400"
  backgroundGradient="from-primary-600 via-primary-700 to-secondary-600"
  backgroundOpacity="15"
  padding="py-16 sm:py-20 md:py-24"
  classes="bg-gradient-to-br from-primary-600 via-primary-700 to-secondary-600"
  [showDecorativeElements]="true">

  <div class="text-center text-white">
    <app-section-title
      title="Join Our Artist Community"
      subtitle="Are you a talented artist passionate about Mithila art? Join our team and showcase your work to art lovers worldwide."
      classes="text-white"
    ></app-section-title>

    <div class="max-w-4xl mx-auto mt-12">
      <div class="grid grid-cols-1 md:grid-cols-3 gap-8 mb-12">
        <!-- Benefit 1 -->
        <div class="text-center">
          <div class="w-16 h-16 bg-white/20 backdrop-blur-sm rounded-full flex items-center justify-center mx-auto mb-4">
            <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
            </svg>
          </div>
          <h3 class="text-xl font-bold mb-2">Earn Income</h3>
          <p class="text-white/80">Sell your artwork and earn a fair share of the profits from each sale.</p>
        </div>

        <!-- Benefit 2 -->
        <div class="text-center">
          <div class="w-16 h-16 bg-white/20 backdrop-blur-sm rounded-full flex items-center justify-center mx-auto mb-4">
            <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 12a9 9 0 01-9 9m9-9a9 9 0 00-9-9m9 9H3m9 9v-9m0-9v9"></path>
            </svg>
          </div>
          <h3 class="text-xl font-bold mb-2">Global Reach</h3>
          <p class="text-white/80">Showcase your art to customers around the world through our platform.</p>
        </div>

        <!-- Benefit 3 -->
        <div class="text-center">
          <div class="w-16 h-16 bg-white/20 backdrop-blur-sm rounded-full flex items-center justify-center mx-auto mb-4">
            <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"></path>
            </svg>
          </div>
          <h3 class="text-xl font-bold mb-2">Community Support</h3>
          <p class="text-white/80">Join a supportive community of artists and learn from each other.</p>
        </div>
      </div>

      <div class="bg-white/10 backdrop-blur-md rounded-2xl p-8 border border-white/20">
        <h3 class="text-2xl font-bold mb-6">Ready to Join Us?</h3>
        <p class="text-white/90 mb-8 text-lg">
          We're looking for passionate artists who specialize in traditional Mithila art forms.
          Whether you're experienced or just starting, we welcome artists who are dedicated to preserving and promoting this beautiful art form.
        </p>

        <div class="flex flex-wrap justify-center gap-4">
          <button (click)="openArtistApplicationForm()"
                  class="bg-white text-primary-600 px-8 py-4 rounded-full font-bold hover:bg-gray-100 transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-xl">
            <span class="flex items-center">
              <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"></path>
              </svg>
              Apply to Join as Artist
            </span>
          </button>
          <a routerLink="/contact"
             class="bg-white/10 backdrop-blur-md text-white px-8 py-4 rounded-full font-bold hover:bg-white/20 transition-all duration-300 transform hover:scale-105 border border-white/30">
            <span class="flex items-center">
              <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"></path>
              </svg>
              Contact Us First
            </span>
          </a>
        </div>
      </div>
    </div>
  </div>
</app-mithila-section>

<!-- Artist Application Form Modal -->
<app-membership-form
  [isOpen]="isArtistApplicationFormOpen"
  (close)="closeArtistApplicationForm()"
  (submit)="onArtistApplicationSubmit($event)"
  [title]="'Join as Artist'">
</app-membership-form>


