<!-- <PERSON> Banner -->
<div class="relative h-[50vh] overflow-hidden">
  <!-- Background Image -->
  <div class="absolute inset-0 bg-cover bg-center bg-no-repeat"
       style="background-image: url('https://i.etsystatic.com/43638819/r/il/bba73f/5930357734/il_794xN.5930357734_1ql7.jpg')">
  </div>

  <!-- Gradient Overlay -->
  <div class="absolute inset-0 bg-gradient-to-br from-primary-800/90 via-primary-700/90 to-brick-700/90"></div>

  <!-- <PERSON><PERSON><PERSON> Background -->
  <div class="absolute inset-0 opacity-10">
    <app-mithila-art-background
      [primaryColor]="'#C1440E'"
      [secondaryColor]="'#F4B400'"
      opacity="15">
    </app-mithila-art-background>
  </div>

  <!-- Content -->
  <div class="container mx-auto px-4 h-full flex flex-col justify-center items-center text-center relative z-10">
    <!-- Decorative Element -->
    <app-mithila-decorative-element
      [primaryColor]="'#C1440E'"
      [secondaryColor]="'#F4B400'"
      [type]="'lotus'"
      position="relative mb-6"
      classes="opacity-90"
      size="80px">
    </app-mithila-decorative-element>

    <!-- Title -->
    <h1 class="text-3xl sm:text-4xl md:text-5xl lg:text-6xl font-bold text-white mb-4">
      Our Artists
    </h1>

    <!-- Subtitle -->
    <p class="text-xl text-white/90 mb-8 max-w-3xl mx-auto">
      Meet the talented artists and shop their beautiful creations
    </p>
  </div>
</div>

<!-- Artists Section -->
<app-mithila-section
  primaryColor="#F4B400"
  secondaryColor="#264653"
  backgroundGradient="from-secondary-50 via-background-light to-accent-50"
  backgroundOpacity="15"
  padding="py-16 sm:py-20 md:py-24"
  [showDecorativeElements]="true">

  <app-section-title
    title="Our Artists"
    subtitle="Meet our talented artists and shop their beautiful creations"
  ></app-section-title>

  <!-- Artists Grid -->
  <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-8">
    <div *ngFor="let artist of featuredArtists" class="bg-white rounded-lg overflow-hidden shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-2">
      <!-- Artist Image -->
      <div class="relative overflow-hidden h-64">
        <img [src]="artist.image" [alt]="artist.name" class="w-full h-full object-cover object-center transition-transform duration-700 hover:scale-110">

        <!-- Overlay -->
        <div class="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent opacity-0 hover:opacity-100 transition-opacity duration-300"></div>

        <!-- Artist Info Overlay -->
        <div class="absolute bottom-0 left-0 right-0 p-4 text-white transform translate-y-full hover:translate-y-0 transition-transform duration-300">
          <h3 class="font-bold text-lg mb-1">{{artist.name}}</h3>
          <p class="text-sm opacity-90">{{artist.role}}</p>
        </div>
      </div>

      <!-- Artist Details -->
      <div class="p-6">
        <h3 class="text-xl font-bold text-gray-900 mb-2">{{artist.name}}</h3>
        <p class="text-primary-600 mb-3 font-medium">{{artist.role}}</p>
        <p class="text-gray-700 mb-4 text-sm line-clamp-3">{{artist.bio}}</p>

        <!-- Artworks -->
        <div class="space-y-3">
          <h4 class="font-semibold text-gray-900 text-sm">Available Artworks:</h4>
          <div *ngFor="let artwork of artist.artworks" class="flex items-center justify-between p-3 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors duration-200">
            <div class="flex-1">
              <p class="font-medium text-gray-900 text-sm">{{artwork.name}}</p>
              <p class="text-primary-600 font-bold text-sm">{{formatPrice(artwork.price)}}</p>
            </div>
            <button
              (click)="addToCart(artwork, $event)"
              [disabled]="isInCart(artwork.id)"
              class="px-3 py-1 text-xs font-medium rounded-full transition-all duration-200"
              [ngClass]="isInCart(artwork.id) ?
                'bg-green-100 text-green-700 cursor-not-allowed' :
                'bg-primary-600 text-white hover:bg-primary-700 hover:shadow-md transform hover:scale-105'">
              {{isInCart(artwork.id) ? 'In Cart' : 'Add to Cart'}}
            </button>
          </div>
        </div>

        <!-- View Profile Link -->
        <div class="mt-4 pt-4 border-t border-gray-200">
          <a [routerLink]="['/artists', artist.id]" class="text-primary-600 hover:text-primary-700 font-medium text-sm flex items-center">
            View Full Profile
            <svg class="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
            </svg>
          </a>
        </div>
      </div>
    </div>
  </div>
</app-mithila-section>


