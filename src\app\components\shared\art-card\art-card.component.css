/* Enhanced Art Card Styles */

/* Card hover effects */
.art-card {
  transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
  transform-origin: center;
}

.art-card:hover {
  transform: translateY(-12px) scale(1.02);
  box-shadow:
    0 25px 50px -12px rgba(0, 0, 0, 0.25),
    0 0 0 1px rgba(193, 68, 14, 0.1),
    0 0 30px rgba(193, 68, 14, 0.15);
}

/* Image zoom effect */
.image-container {
  overflow: hidden;
  position: relative;
}

.image-container img {
  transition: transform 0.7s cubic-bezier(0.4, 0, 0.2, 1);
}

.art-card:hover .image-container img {
  transform: scale(1.15);
}

/* Badge animations */
.badge {
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
}

.featured-badge {
  animation: featuredPulse 2s infinite;
}

@keyframes featuredPulse {
  0%, 100% {
    transform: scale(1);
    box-shadow: 0 0 0 0 rgba(244, 180, 0, 0.7);
  }
  50% {
    transform: scale(1.05);
    box-shadow: 0 0 0 10px rgba(244, 180, 0, 0);
  }
}

/* Quick action buttons */
.quick-actions {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.quick-action-btn {
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
}

.quick-action-btn:hover {
  transform: scale(1.1) rotate(5deg);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

/* Wishlist heart animation */
.wishlist-btn.active {
  animation: heartBeat 0.6s ease-in-out;
}

@keyframes heartBeat {
  0% { transform: scale(1); }
  25% { transform: scale(1.3); }
  50% { transform: scale(1.1); }
  75% { transform: scale(1.25); }
  100% { transform: scale(1); }
}

/* Price highlight effect */
.price {
  position: relative;
  transition: all 0.3s ease;
}

.art-card:hover .price {
  transform: scale(1.05);
  text-shadow: 0 2px 4px rgba(193, 68, 14, 0.3);
}

/* Button hover effects */
.add-to-cart-btn {
  position: relative;
  overflow: hidden;
  transition: all 0.3s ease;
}

.add-to-cart-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.add-to-cart-btn:hover::before {
  left: 100%;
}

.add-to-cart-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(193, 68, 14, 0.3);
}

/* View details button */
.view-details-btn {
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.view-details-btn::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 0;
  height: 2px;
  background: linear-gradient(90deg, #C1440E, #F4B400);
  transition: width 0.3s ease;
}

.view-details-btn:hover::after {
  width: 100%;
}

/* Text truncation */
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* Glow effect */
.glow-effect {
  transition: all 0.3s ease;
}

.art-card:hover .glow-effect {
  box-shadow:
    inset 0 0 20px rgba(193, 68, 14, 0.1),
    0 0 40px rgba(193, 68, 14, 0.2);
}

/* Loading state */
.loading-shimmer {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: shimmer 2s infinite;
}

@keyframes shimmer {
  0% { background-position: -200% 0; }
  100% { background-position: 200% 0; }
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .art-card:hover {
    transform: translateY(-6px) scale(1.01);
  }

  .quick-actions {
    opacity: 1;
    transform: translateY(0);
  }

  .add-to-cart-btn,
  .view-details-btn {
    padding: 0.5rem 1rem;
    font-size: 0.875rem;
  }
}

/* Accessibility improvements */
.art-card:focus-within {
  outline: 2px solid #C1440E;
  outline-offset: 2px;
}

.quick-action-btn:focus,
.add-to-cart-btn:focus,
.view-details-btn:focus {
  outline: 2px solid #C1440E;
  outline-offset: 2px;
}

/* Print styles */
@media print {
  .art-card {
    break-inside: avoid;
    box-shadow: none;
    border: 1px solid #ddd;
  }

  .quick-actions,
  .add-to-cart-btn {
    display: none;
  }
}
