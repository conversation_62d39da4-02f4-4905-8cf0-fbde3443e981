import { Component, OnInit } from '@angular/core';
import { RouterLink, RouterLinkActive } from '@angular/router';
import { CommonModule } from '@angular/common';
import { CartService } from '../../../services/cart.service';
import { MembershipFormComponent } from '../../shared/membership-form/membership-form.component';

@Component({
  selector: 'app-header',
  standalone: true,
  imports: [RouterLink, RouterLinkActive, CommonModule, MembershipFormComponent],
  templateUrl: './header.component.html',
  styleUrl: './header.component.css'
})
export class HeaderComponent implements OnInit {
  isMenuOpen = false;
  cartItemCount = 0;
  isMembershipFormOpen = false;

  constructor(private cartService: CartService) {}

  ngOnInit() {
    this.cartService.cart$.subscribe(cartItems => {
      this.cartItemCount = this.cartService.getCartItemCount();
    });
  }

  toggleMenu() {
    this.isMenuOpen = !this.isMenuOpen;
  }

  openMembershipForm() {
    this.isMembershipFormOpen = true;
  }

  closeMembershipForm() {
    this.isMembershipFormOpen = false;
  }

  onMembershipSubmit(formData: any) {
    console.log('Membership application submitted:', formData);
    // Here you would typically send the data to your backend
  }
}
