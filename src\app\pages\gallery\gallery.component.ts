import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { HeroBannerComponent } from '../../components/shared/hero-banner/hero-banner.component';
import { SectionTitleComponent } from '../../components/shared/section-title/section-title.component';
import { MithilaSectionComponent } from '../../components/shared/mithila-section/mithila-section.component';
import { MithilaArtBackgroundComponent } from '../../components/shared/mithila-art-background/mithila-art-background.component';
import { MithilaBorderComponent } from '../../components/shared/mithila-border/mithila-border.component';
import { MithilaDecorativeElementComponent } from '../../components/shared/mithila-decorative-element/mithila-decorative-element.component';
import { LightboxComponent, LightboxImage } from '../../components/shared/lightbox/lightbox.component';
import { RouterLink } from '@angular/router';

@Component({
  selector: 'app-gallery',
  standalone: true,
  imports: [
    CommonModule,
    SectionTitleComponent,
    MithilaSectionComponent,
    MithilaArtBackgroundComponent,
    MithilaBorderComponent,
    MithilaDecorativeElementComponent,
    LightboxComponent
  ],
  templateUrl: './gallery.component.html',
  styleUrl: './gallery.component.css'
})
export class GalleryComponent {
  selectedCategory = 'All';
  isLightboxOpen = false;
  currentImageIndex = 0;

  categories = [
    'All',
    'Traditional',
    'Contemporary',
    'Religious',
    'Nature & Wildlife',
    'Portraits',
    'Abstract'
  ];

  galleryImages: LightboxImage[] = [
    {
      src: 'https://i.etsystatic.com/43638819/r/il/bba73f/5930357734/il_794xN.5930357734_1ql7.jpg',
      alt: 'Traditional Mithila Art',
      title: 'Madhubani Fish',
      artist: 'Sarita Devi',
      category: 'Traditional',
      description: 'A beautiful traditional Madhubani painting featuring fish motifs, symbolizing prosperity and good fortune.'
    },
    {
      src: 'https://i.etsystatic.com/43638819/r/il/74f862/5978434641/il_794xN.5978434641_4mc6.jpg',
      alt: 'Peacock Mithila Art',
      title: 'Royal Peacock',
      artist: 'Kamala Devi',
      category: 'Traditional',
      description: 'Intricate peacock design representing beauty and grace in traditional Mithila art style.'
    },
    {
      src: 'https://i.etsystatic.com/43638819/r/il/372e12/5978434665/il_794xN.5978434665_7bre.jpg',
      alt: 'Lotus Mithila Art',
      title: 'Sacred Lotus',
      artist: 'Sunita Jha',
      category: 'Religious',
      description: 'Sacred lotus motifs with intricate geometric patterns, representing purity and spiritual awakening.'
    },
    {
      src: 'https://i.etsystatic.com/43638819/r/il/7c167c/5978434677/il_794xN.5978434677_mkhf.jpg',
      alt: 'Tree of Life',
      title: 'Tree of Life',
      artist: 'Meera Sharma',
      category: 'Nature & Wildlife',
      description: 'A magnificent tree of life painting showcasing the connection between earth and sky.'
    },
    {
      src: 'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=800',
      alt: 'Contemporary Mithila',
      title: 'Modern Fusion',
      artist: 'Priya Kumari',
      category: 'Contemporary',
      description: 'A contemporary interpretation of traditional Mithila art with modern color palettes.'
    },
    {
      src: 'https://images.unsplash.com/photo-1541961017774-22349e4a1262?w=800',
      alt: 'Portrait Art',
      title: 'Village Woman',
      artist: 'Rekha Devi',
      category: 'Portraits',
      description: 'A beautiful portrait of a village woman adorned with traditional jewelry and clothing.'
    },
    {
      src: 'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=800',
      alt: 'Abstract Mithila',
      title: 'Geometric Dreams',
      artist: 'Anita Singh',
      category: 'Abstract',
      description: 'Abstract interpretation of traditional Mithila patterns with bold geometric designs.'
    },
    {
      src: 'https://images.unsplash.com/photo-1541961017774-22349e4a1262?w=800',
      alt: 'Religious Art',
      title: 'Ganesha Blessing',
      artist: 'Sita Devi',
      category: 'Religious',
      description: 'Lord Ganesha depicted in traditional Mithila style, bringing blessings and removing obstacles.'
    }
  ];

  get filteredImages(): LightboxImage[] {
    if (this.selectedCategory === 'All') {
      return this.galleryImages;
    }
    return this.galleryImages.filter(image => image.category === this.selectedCategory);
  }

  selectCategory(category: string) {
    this.selectedCategory = category;
  }

  openLightbox(index: number) {
    this.currentImageIndex = index;
    this.isLightboxOpen = true;
  }

  closeLightbox() {
    this.isLightboxOpen = false;
  }

  onImageIndexChange(index: number) {
    this.currentImageIndex = index;
  }
}
