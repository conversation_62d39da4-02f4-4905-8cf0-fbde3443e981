import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterLink } from '@angular/router';
import { SectionTitleComponent } from '../../components/shared/section-title/section-title.component';
import { MithilaSectionComponent } from '../../components/shared/mithila-section/mithila-section.component';
import { MithilaArtBackgroundComponent } from '../../components/shared/mithila-art-background/mithila-art-background.component';
import { MithilaBorderComponent } from '../../components/shared/mithila-border/mithila-border.component';
import { MithilaDecorativeElementComponent } from '../../components/shared/mithila-decorative-element/mithila-decorative-element.component';
import { CartService, CartItem } from '../../services/cart.service';

@Component({
  selector: 'app-artists',
  standalone: true,
  imports: [
    CommonModule,
    RouterLink,
    SectionTitleComponent,
    MithilaSectionComponent,
    MithilaArtBackgroundComponent,
    Mi<PERSON>laBorderComponent,
    MithilaDecorativeElementComponent
  ],
  templateUrl: './artists.component.html',
  styleUrl: './artists.component.css'
})
export class ArtistsComponent {
  constructor(private cartService: CartService) {}

  // Featured artists with their artworks
  featuredArtists = [
    {
      id: '1',
      name: 'Sarita Devi',
      role: 'Master Artist & Founder',
      bio: 'With over 25 years of experience in traditional Mithila art, Sarita founded Mithilani Ghar to preserve and promote this unique cultural heritage.',
      image: 'https://i.etsystatic.com/43638819/r/il/bba73f/5930357734/il_794xN.5930357734_1ql7.jpg',
      artworks: [
        {
          id: 'art-1',
          name: 'Traditional Fish Motif',
          slug: 'traditional-fish-motif',
          price: 15000,
          image: 'https://i.etsystatic.com/43638819/r/il/bba73f/5930357734/il_794xN.5930357734_1ql7.jpg',
          artist: 'Sarita Devi'
        },
        {
          id: 'art-2',
          name: 'Village Life Celebration',
          slug: 'village-life-celebration',
          price: 25000,
          image: 'https://i.etsystatic.com/43638819/r/il/bba73f/5930357734/il_794xN.5930357734_1ql7.jpg',
          artist: 'Sarita Devi'
        }
      ]
    },
    {
      id: '2',
      name: 'Ramesh Kumar',
      role: 'Contemporary Artist',
      bio: 'Ramesh blends traditional Mithila techniques with contemporary themes and styles. His innovative approach has brought Mithila art to new audiences.',
      image: 'https://i.etsystatic.com/43638819/r/il/74f862/5978434641/il_794xN.5978434641_4mc6.jpg',
      artworks: [
        {
          id: 'art-3',
          name: 'Modern Peacock Design',
          slug: 'modern-peacock-design',
          price: 18000,
          image: 'https://i.etsystatic.com/43638819/r/il/74f862/5978434641/il_794xN.5978434641_4mc6.jpg',
          artist: 'Ramesh Kumar'
        },
        {
          id: 'art-4',
          name: 'Urban Mithila',
          slug: 'urban-mithila',
          price: 22000,
          image: 'https://i.etsystatic.com/43638819/r/il/74f862/5978434641/il_794xN.5978434641_4mc6.jpg',
          artist: 'Ramesh Kumar'
        }
      ]
    },
    {
      id: '3',
      name: 'Anita Jha',
      role: 'Master Artist & Instructor',
      bio: 'Anita specializes in traditional Mithila painting techniques and leads our workshops. Her work focuses on religious and mythological themes.',
      image: 'https://i.etsystatic.com/43638819/r/il/372e12/5978434665/il_794xN.5978434665_7bre.jpg',
      artworks: [
        {
          id: 'art-5',
          name: 'Sacred Lotus',
          slug: 'sacred-lotus',
          price: 20000,
          image: 'https://i.etsystatic.com/43638819/r/il/372e12/5978434665/il_794xN.5978434665_7bre.jpg',
          artist: 'Anita Jha'
        },
        {
          id: 'art-6',
          name: 'Divine Feminine',
          slug: 'divine-feminine',
          price: 28000,
          image: 'https://i.etsystatic.com/43638819/r/il/372e12/5978434665/il_794xN.5978434665_7bre.jpg',
          artist: 'Anita Jha'
        }
      ]
    },
    {
      id: '4',
      name: 'Sunil Yadav',
      role: 'Nature & Wildlife Artist',
      bio: 'Sunil\'s work celebrates the natural world through Mithila art, featuring detailed depictions of local flora and fauna with environmental conservation messages.',
      image: 'https://i.etsystatic.com/43638819/r/il/7c167c/5978434677/il_794xN.5978434677_mkhf.jpg',
      artworks: [
        {
          id: 'art-7',
          name: 'Tree of Life',
          slug: 'tree-of-life',
          price: 16000,
          image: 'https://i.etsystatic.com/43638819/r/il/7c167c/5978434677/il_794xN.5978434677_mkhf.jpg',
          artist: 'Sunil Yadav'
        },
        {
          id: 'art-8',
          name: 'Harmony of Nature',
          slug: 'harmony-of-nature',
          price: 19000,
          image: 'https://i.etsystatic.com/43638819/r/il/7c167c/5978434677/il_794xN.5978434677_mkhf.jpg',
          artist: 'Sunil Yadav'
        }
      ]
    }
  ];
  addToCart(artwork: any, event: Event) {
    event.preventDefault();
    event.stopPropagation();

    const cartItem: Omit<CartItem, 'quantity'> = {
      id: artwork.id,
      name: artwork.name,
      slug: artwork.slug,
      price: artwork.price,
      image: artwork.image,
      artist: artwork.artist
    };

    this.cartService.addToCart(cartItem, 1);
  }

  isInCart(artworkId: string): boolean {
    return this.cartService.isInCart(artworkId);
  }

  formatPrice(price: number): string {
    return `NPR ${price.toLocaleString()}`;
  }
}
