<!-- Enhanced Art Card -->
<div class="group relative bg-white rounded-2xl shadow-lg hover:shadow-2xl transition-all duration-500 transform hover:-translate-y-3 overflow-hidden border border-gray-100 hover:border-primary-200 cursor-pointer"
     (click)="onCardClick()">

  <!-- Image Container -->
  <div class="relative overflow-hidden aspect-square">
    <a [routerLink]="['/products', id]" class="block">
      <img [src]="imageUrl" [alt]="title"
           class="w-full h-full object-cover transition-transform duration-700 group-hover:scale-110">
    </a>

    <!-- Image Overlay -->
    <div class="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>

    <!-- Badges -->
    <div class="absolute top-4 left-4 flex flex-col space-y-2">
      <span *ngIf="featured"
            class="bg-gradient-to-r from-secondary-500 to-secondary-600 text-white px-3 py-1 rounded-full text-xs font-bold shadow-lg animate-pulse flex items-center">
        <svg class="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 24 24">
          <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
        </svg>
        Featured
      </span>

      <span *ngIf="category"
            class="bg-white/90 backdrop-blur-sm text-primary-600 px-3 py-1 rounded-full text-xs font-medium shadow-md">
        {{category}}
      </span>
    </div>



    <!-- Quick Actions -->
    <div class="absolute bottom-4 right-4 flex space-x-2 opacity-0 group-hover:opacity-100 transition-all duration-300 transform translate-y-2 group-hover:translate-y-0">
      <!-- Wishlist Button -->
      <button (click)="onToggleWishlist($event)"
              class="p-2 bg-white/90 backdrop-blur-sm rounded-full shadow-lg hover:bg-white hover:scale-110 transition-all duration-300"
              [class.text-red-500]="isWishlisted"
              [class.text-gray-600]="!isWishlisted">
        <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
          <path d="M12 21.35l-1.45-1.32C5.4 15.36 2 12.28 2 8.5 2 5.42 4.42 3 7.5 3c1.74 0 3.41.81 4.5 2.09C13.09 3.81 14.76 3 16.5 3 19.58 3 22 5.42 22 8.5c0 3.78-3.4 6.86-8.55 11.54L12 21.35z"/>
        </svg>
      </button>

      <!-- Quick View Button -->
      <a [routerLink]="['/products', id]"
         class="p-2 bg-white/90 backdrop-blur-sm rounded-full shadow-lg hover:bg-white hover:scale-110 transition-all duration-300 text-gray-600 hover:text-primary-600">
        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
        </svg>
      </a>
    </div>

    <!-- Decorative Element -->
    <app-mithila-decorative-element
      [primaryColor]="'#F4B400'"
      [secondaryColor]="'#C1440E'"
      [type]="'lotus'"
      position="absolute -bottom-2 -left-2"
      classes="opacity-10 group-hover:opacity-20 transition-opacity duration-300"
      size="40px">
    </app-mithila-decorative-element>
  </div>

  <!-- Content -->
  <div class="p-6">
    <!-- Title and Artist -->
    <div class="mb-4">
      <h3 class="text-lg font-bold text-gray-900 mb-2 line-clamp-2 group-hover:text-primary-600 transition-colors duration-300 relative">
        <span class="relative">
          {{title}}
          <span class="absolute bottom-0 left-0 w-0 h-0.5 bg-primary-600 transition-all duration-300 group-hover:w-full"></span>
        </span>
      </h3>
      <p class="text-sm text-gray-600 flex items-center">
        <svg class="w-4 h-4 mr-1 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
        </svg>
        By <span class="text-primary-600 font-medium ml-1">{{artist}}</span>
      </p>
    </div>

    <!-- Details -->
    <div class="mb-4 space-y-1">
      <p class="text-xs text-gray-500 flex items-center">
        <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 21a4 4 0 01-4-4V5a2 2 0 012-2h4a2 2 0 012 2v12a4 4 0 01-4 4zM21 5a2 2 0 00-2-2h-4a2 2 0 00-2 2v12a4 4 0 004 4h4a2 2 0 002-2V5z"></path>
        </svg>
        {{medium}}
      </p>
      <p class="text-xs text-gray-500 flex items-center">
        <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 8V4m0 0h4M4 4l5 5m11-1V4m0 0h-4m4 0l-5 5M4 16v4m0 0h4m-4 0l5-5m11 5l-5-5m5 5v-4m0 4h-4"></path>
        </svg>
        {{size}}
      </p>
    </div>

    <!-- Price and Actions -->
    <div class="flex items-center justify-between">
      <div class="flex flex-col">
        <span class="text-2xl font-bold text-primary-600">{{formatPrice(price)}}</span>
        <span *ngIf="isInCart" class="text-sm text-green-600 font-medium flex items-center">
          <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
          </svg>
          In Cart
        </span>
      </div>

      <!-- Add to Cart Button -->
      <button *ngIf="!isInCart"
              (click)="onAddToCart($event)"
              class="px-6 py-3 bg-gradient-to-r from-primary-600 to-primary-700 text-white rounded-lg hover:from-primary-700 hover:to-primary-800 transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-xl font-medium flex items-center">
        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 3h2l.4 2M7 13h10l4-8H5.4m0 0L7 13m0 0l-1.5 6M7 13l-1.5 6m0 0h9"></path>
        </svg>
        Add to Cart
      </button>

      <!-- Already in Cart Button -->
      <button *ngIf="isInCart"
              class="px-6 py-3 bg-green-600 text-white rounded-lg font-medium flex items-center cursor-default">
        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
        </svg>
        Added to Cart
      </button>
    </div>
  </div>

  <!-- Hover Glow Effect -->
  <div class="absolute inset-0 rounded-2xl opacity-0 group-hover:opacity-100 transition-opacity duration-300 pointer-events-none"
       style="box-shadow: 0 0 30px rgba(193, 68, 14, 0.2);"></div>
</div>
